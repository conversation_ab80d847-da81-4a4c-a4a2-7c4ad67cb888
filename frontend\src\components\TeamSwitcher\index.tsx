/**
 * 团队切换组件
 * 显示当前团队信息并提供团队切换功能
 */

import React, { useState, useEffect } from 'react';
import { Button, Dropdown, Space, Typography, Avatar, message, Spin } from 'antd';
import { TeamOutlined, SwapOutlined, DownOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { createStyles } from 'antd-style';
import { TeamService, AuthService } from '@/services';
import type { TeamDetailResponse } from '@/types/api';

const { Text } = Typography;

const useStyles = createStyles(({ token }) => ({
  teamSwitcher: {
    display: 'flex',
    alignItems: 'center',
    padding: '4px 12px',
    borderRadius: token.borderRadius,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    cursor: 'pointer',
    transition: 'all 0.3s',
    '&:hover': {
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      borderColor: 'rgba(255, 255, 255, 0.3)',
    },
  },
  teamInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: 8,
  },
  teamName: {
    color: 'rgba(255, 255, 255, 0.85)',
    fontWeight: 500,
    maxWidth: 150,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
  dropdownItem: {
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    padding: '8px 12px',
    minWidth: 200,
  },
  currentTeamItem: {
    backgroundColor: token.colorPrimaryBg,
  },
}));

interface TeamSwitcherProps {
  style?: React.CSSProperties;
}

const TeamSwitcher: React.FC<TeamSwitcherProps> = ({ style }) => {
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const { styles } = useStyles();
  const { initialState, setInitialState } = useModel('@@initialState');

  const currentTeam = initialState?.currentTeam;

  // 获取用户的所有团队
  const fetchTeams = async () => {
    try {
      setLoading(true);
      const teamList = await TeamService.getUserTeams();
      setTeams(teamList);
    } catch (error) {
      console.error('获取团队列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (dropdownVisible) {
      fetchTeams();
    }
  }, [dropdownVisible]);

  // 切换团队
  const handleTeamSwitch = async (teamId: number) => {
    if (teamId === currentTeam?.id) {
      setDropdownVisible(false);
      return;
    }

    try {
      setLoading(true);
      await AuthService.selectTeam({ teamId });
      message.success('团队切换成功！');

      // 刷新 initialState 以更新团队信息
      if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {
        const [currentUser, currentTeam] = await Promise.all([
          initialState.fetchUserInfo(),
          initialState.fetchTeamInfo()
        ]);

        setInitialState({
          ...initialState,
          currentUser,
          currentTeam,
        });
      }

      setDropdownVisible(false);
      
      // 刷新当前页面以确保数据更新
      window.location.reload();
    } catch (error) {
      console.error('团队切换失败:', error);
      message.error('团队切换失败');
    } finally {
      setLoading(false);
    }
  };

  // 跳转到团队管理页面
  const handleManageTeams = () => {
    setDropdownVisible(false);
    history.push('/team/list');
  };

  // 跳转到团队选择页面
  const handleSelectTeam = () => {
    setDropdownVisible(false);
    history.push('/user/team-select');
  };

  if (!currentTeam) {
    return null;
  }

  const dropdownItems = [
    // 当前团队
    {
      key: 'current',
      label: (
        <div className={`${styles.dropdownItem} ${styles.currentTeamItem}`}>
          <Avatar size="small" icon={<TeamOutlined />} />
          <div>
            <div style={{ fontWeight: 500 }}>{currentTeam.name}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>当前团队</Text>
          </div>
        </div>
      ),
      disabled: true,
    },
    {
      type: 'divider' as const,
    },
    // 其他团队
    ...teams
      .filter(team => team.id !== currentTeam.id)
      .map(team => ({
        key: team.id.toString(),
        label: (
          <div className={styles.dropdownItem}>
            <Avatar size="small" icon={<TeamOutlined />} />
            <div>
              <div>{team.name}</div>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {team.memberCount} 名成员
              </Text>
            </div>
          </div>
        ),
        onClick: () => handleTeamSwitch(team.id),
      })),
    {
      type: 'divider' as const,
    },
    // 管理选项
    {
      key: 'select',
      label: (
        <div className={styles.dropdownItem}>
          <SwapOutlined />
          <span>选择团队</span>
        </div>
      ),
      onClick: handleSelectTeam,
    },
    {
      key: 'manage',
      label: (
        <div className={styles.dropdownItem}>
          <TeamOutlined />
          <span>管理团队</span>
        </div>
      ),
      onClick: handleManageTeams,
    },
  ];

  return (
    <Dropdown
      menu={{ items: dropdownItems }}
      trigger={['click']}
      open={dropdownVisible}
      onOpenChange={setDropdownVisible}
      placement="bottomRight"
    >
      <div className={styles.teamSwitcher} style={style}>
        <Spin spinning={loading} size="small">
          <div className={styles.teamInfo}>
            <Avatar size="small" icon={<TeamOutlined />} />
            <Text className={styles.teamName}>{currentTeam.name}</Text>
            <DownOutlined style={{ color: 'rgba(255, 255, 255, 0.65)', fontSize: 12 }} />
          </div>
        </Spin>
      </div>
    </Dropdown>
  );
};

export default TeamSwitcher;
