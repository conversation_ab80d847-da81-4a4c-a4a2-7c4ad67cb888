/**
 * 团队列表内容组件
 */

import React, { useState, useEffect } from 'react';
import { 
  List, 
  Avatar, 
  Button, 
  Space, 
  Typography, 
  Tag, 
  Input,
  message,
  Empty
} from 'antd';
import { 
  TeamOutlined, 
  UserOutlined, 
  SearchOutlined,
  CrownOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { history } from '@umijs/max';
import { TeamService, AuthService } from '@/services';
import type { TeamDetailResponse } from '@/types/api';

const { Title, Text } = Typography;
const { Search } = Input;

const TeamListContent: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [filteredTeams, setFilteredTeams] = useState<TeamDetailResponse[]>([]);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchTeams();
  }, []);

  useEffect(() => {
    // 过滤团队列表
    const filtered = teams.filter(team =>
      team.name.toLowerCase().includes(searchText.toLowerCase()) ||
      (team.description && team.description.toLowerCase().includes(searchText.toLowerCase()))
    );
    setFilteredTeams(filtered);
  }, [teams, searchText]);

  const fetchTeams = async () => {
    try {
      setLoading(true);
      const teamList = await TeamService.getUserTeams();
      setTeams(teamList);
    } catch (error) {
      console.error('获取团队列表失败:', error);
      message.error('获取团队列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTeam = () => {
    history.push('/team/create');
  };

  const handleViewTeam = async (team: TeamDetailResponse) => {
    try {
      // 切换到该团队
      await AuthService.teamLogin({ teamId: team.id });
      // 刷新页面以更新当前团队状态
      window.location.reload();
    } catch (error) {
      console.error('切换团队失败:', error);
    }
  };

  const handleSwitchTeam = async (team: TeamDetailResponse) => {
    try {
      await AuthService.teamLogin({ teamId: team.id });
      message.success(`已切换到团队：${team.name}`);
      // 跳转到主页
      history.push('/');
    } catch (error) {
      console.error('切换团队失败:', error);
    }
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Search
          placeholder="搜索团队名称或描述"
          allowClear
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300 }}
        />
      </div>

      {filteredTeams.length === 0 && !loading ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            searchText ? '没有找到匹配的团队' : '您还没有加入任何团队'
          }
        >
          {!searchText && (
            <Button type="primary" onClick={handleCreateTeam}>
              创建第一个团队
            </Button>
          )}
        </Empty>
      ) : (
        <List
          loading={loading}
          itemLayout="horizontal"
          dataSource={filteredTeams}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个团队`,
          }}
          renderItem={(team) => (
            <List.Item
              actions={[
                <Button
                  key="view"
                  type="text"
                  icon={<EyeOutlined />}
                  onClick={() => handleViewTeam(team)}
                >
                  查看详情
                </Button>,
                <Button
                  key="switch"
                  type="primary"
                  size="small"
                  onClick={() => handleSwitchTeam(team)}
                >
                  进入团队
                </Button>,
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar 
                    size={64} 
                    icon={<TeamOutlined />}
                    style={{ backgroundColor: '#1890ff' }}
                  />
                }
                title={
                  <Space>
                    <Title level={4} style={{ margin: 0 }}>
                      {team.name}
                    </Title>
                    {team.isCreator && (
                      <Tag color="gold" icon={<CrownOutlined />}>
                        创建者
                      </Tag>
                    )}
                  </Space>
                }
                description={
                  <Space direction="vertical" size="small">
                    {team.description && (
                      <Text type="secondary">{team.description}</Text>
                    )}
                    <Space>
                      <Space size="small">
                        <UserOutlined />
                        <Text type="secondary">{team.memberCount} 名成员</Text>
                      </Space>
                      <Text type="secondary">
                        创建于 {new Date(team.createdAt).toLocaleDateString()}
                      </Text>
                      <Text type="secondary">
                        更新于 {new Date(team.updatedAt).toLocaleDateString()}
                      </Text>
                    </Space>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      )}
    </div>
  );
};

export default TeamListContent;
