/**
 * 团队选择页面
 * 实现双阶段认证的第二阶段：团队登录
 */

import React, { useState, useEffect } from 'react';
import { Card, Typography, Space, message, Spin } from 'antd';
import { TeamOutlined } from '@ant-design/icons';
import { history, useLocation, useModel } from '@umijs/max';
import { createStyles } from 'antd-style';
import { AuthService, TeamService } from '@/services';
import type { TeamInfo } from '@/types/api';
import { TeamList, ActionButtons } from './components';
import { setTeamSelectionInProgress } from '@/utils/teamSelectionState';

const { Title, Text } = Typography;

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundColor: token.colorBgLayout,
    },
    content: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '32px 16px',
    },
    header: {
      marginBottom: 40,
      textAlign: 'center',
    },
    teamCard: {
      width: '100%',
      maxWidth: 600,
      marginBottom: 24,
    },
  };
});

const TeamSelectPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [selectedTeamId, setSelectedTeamId] = useState<number | null>(null);
  const [teams, setTeams] = useState<TeamInfo[]>([]);
  const { styles } = useStyles();
  const location = useLocation();
  const { initialState, setInitialState } = useModel('@@initialState');

  useEffect(() => {
    // 从路由状态获取团队列表
    const teamsFromState = location.state?.teams;
    if (teamsFromState) {
      setTeams(teamsFromState);
      // 不自动选中团队，需要用户手动选择
    } else {
      // 如果没有团队数据，重新获取
      fetchTeams();
    }
  }, [location.state]);

  const fetchTeams = async () => {
    try {
      setLoading(true);
      const teamList = await TeamService.getUserTeams();
      const teamInfos: TeamInfo[] = teamList.map(team => ({
        id: team.id,
        name: team.name,
        isCreator: team.isCreator,
        memberCount: team.memberCount,
        lastAccessTime: team.updatedAt,
      }));
      setTeams(teamInfos);
    } catch (error) {
      console.error('获取团队列表失败:', error);
      message.error('获取团队列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTeamSelect = async () => {
    if (!selectedTeamId) {
      message.warning('请选择一个团队');
      return;
    }

    setLoading(true);
    // 标记团队选择正在进行中
    setTeamSelectionInProgress(true);
    console.log('🚀 开始团队选择，团队ID:', selectedTeamId);

    try {
      await AuthService.selectTeam({ teamId: selectedTeamId });
      console.log('✅ 团队选择 API 调用成功');
      message.success('团队选择成功！');

      // 等待一小段时间确保 Token 更新完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 刷新 initialState 以更新团队信息（可选，用于提升用户体验）
      if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {
        console.log('🔄 开始更新 initialState');
        try {
          const [currentUser, currentTeam] = await Promise.all([
            initialState.fetchUserInfo(),
            initialState.fetchTeamInfo()
          ]);

          console.log('📊 获取到的用户信息:', currentUser);
          console.log('📊 获取到的团队信息:', currentTeam);

          setInitialState({
            ...initialState,
            currentUser,
            currentTeam,
          });
          console.log('✅ initialState 更新完成');
        } catch (error) {
          console.error('❌ 更新 initialState 失败:', error);
        }
      }

      // 直接跳转，不依赖 initialState 更新
      // 路由守卫会使用 Token 检查，确保正确的跳转逻辑
      console.log('🚀 跳转到仪表板页面');
      history.push('/dashboard');
    } catch (error) {
      console.error('❌ 团队选择失败:', error);
      message.error('团队选择失败，请重试');
    } finally {
      setLoading(false);
      // 无论成功还是失败，都清除团队选择进行中的标记
      setTeamSelectionInProgress(false);
      console.log('🏁 团队选择流程结束');
    }
  };

  // 保持兼容性的方法名
  const handleTeamLogin = handleTeamSelect;

  const handleCreateTeam = () => {
    history.push('/team/create');
  };

  const handleLogout = () => {
    AuthService.clearTokens();
    history.push('/user/login');
  };

  if (loading && teams.length === 0) {
    return (
      <div className={styles.container}>
        <div className={styles.content}>
          <Spin size="large" />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.header}>
          <Space direction="vertical" align="center" size="large">
            <TeamOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            <div>
              <Title level={2}>选择团队</Title>
              <Text type="secondary">请选择要进入的团队工作空间</Text>
            </div>
          </Space>
        </div>

        <Card className={styles.teamCard}>
          <TeamList
            teams={teams}
            selectedTeamId={selectedTeamId}
            onTeamSelect={setSelectedTeamId}
          />
        </Card>

        <ActionButtons
          hasTeams={teams.length > 0}
          selectedTeamId={selectedTeamId}
          loading={loading}
          onTeamLogin={handleTeamLogin}
          onCreateTeam={handleCreateTeam}
          onLogout={handleLogout}
          showCreateButton={true} // 始终显示创建团队按钮
        />
      </div>
    </div>
  );
};

export default TeamSelectPage;
