/**
 * 团队详情内容组件
 */

import React, { useState } from 'react';
import { 
  Descriptions, 
  Button, 
  Space, 
  Typography, 
  message, 
  Modal, 
  Form, 
  Input,
  Spin,
  Divider,
  Empty
} from 'antd';
import {
  TeamOutlined,
  EditOutlined,
  UserAddOutlined,
  SettingOutlined,
  SwapOutlined
} from '@ant-design/icons';
import { TeamService } from '@/services';
import type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';
import TeamMemberList from './TeamMemberList';
import InviteMemberModal from './InviteMemberModal';
import MemberAssignModal from './MemberAssignModal';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface TeamDetailContentProps {
  teamDetail: TeamDetailResponse | null;
  loading: boolean;
  onRefresh: () => void;
}

const TeamDetailContent: React.FC<TeamDetailContentProps> = ({
  teamDetail,
  loading,
  onRefresh
}) => {
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [inviteModalVisible, setInviteModalVisible] = useState(false);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [form] = Form.useForm();

  const handleEdit = () => {
    if (!teamDetail) return;
    form.setFieldsValue({
      name: teamDetail.name,
      description: teamDetail.description,
    });
    setEditModalVisible(true);
  };

  const handleUpdate = async (values: any) => {
    if (!teamDetail) return;
    
    try {
      setUpdating(true);
      const updateData: UpdateTeamRequest = {
        name: values.name,
        description: values.description,
      };
      
      await TeamService.updateTeam(teamDetail.id, updateData);
      setEditModalVisible(false);
      message.success('团队信息更新成功');
      onRefresh();
    } catch (error) {
      console.error('更新团队信息失败:', error);
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!teamDetail) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="请先选择一个团队"
      />
    );
  }

  return (
    <div>
      {/* 团队基本信息 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Space>
            <TeamOutlined style={{ fontSize: 24, color: '#1890ff' }} />
            <Title level={3} style={{ margin: 0 }}>
              {teamDetail.name}
            </Title>
            {teamDetail.isCreator && (
              <Text type="secondary" style={{ fontSize: 14 }}>
                (管理员)
              </Text>
            )}
          </Space>
          {teamDetail.isCreator && (
            <Space>
              <Button
                type="primary"
                icon={<UserAddOutlined />}
                onClick={() => setInviteModalVisible(true)}
              >
                邀请成员
              </Button>
              <Button
                icon={<SwapOutlined />}
                onClick={() => setAssignModalVisible(true)}
              >
                分配成员
              </Button>
              <Button
                icon={<EditOutlined />}
                onClick={handleEdit}
              >
                编辑团队
              </Button>
            </Space>
          )}
        </div>

        <Descriptions column={2} bordered>
          <Descriptions.Item label="团队名称">
            {teamDetail.name}
          </Descriptions.Item>
          <Descriptions.Item label="成员数量">
            {teamDetail.memberCount} 人
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {new Date(teamDetail.createdAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {new Date(teamDetail.updatedAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="团队描述" span={2}>
            {teamDetail.description || '暂无描述'}
          </Descriptions.Item>
        </Descriptions>
      </div>

      <Divider />

      {/* 团队成员列表 */}
      <TeamMemberList teamId={teamDetail.id} isCreator={teamDetail.isCreator} />

      {/* 编辑团队模态框 */}
      <Modal
        title="编辑团队信息"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdate}
        >
          <Form.Item
            label="团队名称"
            name="name"
            rules={[
              { required: true, message: '请输入团队名称' },
              { min: 2, max: 50, message: '团队名称长度应在2-50个字符之间' }
            ]}
          >
            <Input placeholder="请输入团队名称" />
          </Form.Item>

          <Form.Item
            label="团队描述"
            name="description"
            rules={[
              { max: 200, message: '团队描述不能超过200个字符' }
            ]}
          >
            <TextArea
              rows={4}
              placeholder="请输入团队描述（可选）"
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={updating}>
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 邀请成员模态框 */}
      <InviteMemberModal
        visible={inviteModalVisible}
        onCancel={() => setInviteModalVisible(false)}
        teamId={teamDetail.id}
        onSuccess={() => {
          setInviteModalVisible(false);
          onRefresh();
        }}
      />

      {/* 分配成员模态框 */}
      <MemberAssignModal
        visible={assignModalVisible}
        onCancel={() => setAssignModalVisible(false)}
        teamId={teamDetail.id}
        onSuccess={() => {
          setAssignModalVisible(false);
          onRefresh();
        }}
      />
    </div>
  );
};

export default TeamDetailContent;
