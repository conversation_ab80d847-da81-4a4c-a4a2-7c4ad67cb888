/**
 * 用户管理相关 API 服务
 */

import { apiRequest } from '@/utils/request';
import type {
  UserProfileResponse,
  UpdateUserProfileRequest,
} from '@/types/api';

/**
 * 用户服务类
 */
export class UserService {
  /**
   * 获取当前用户资料
   */
  static async getUserProfile(): Promise<UserProfileResponse> {
    const response = await apiRequest.get<UserProfileResponse>('/users/profile');
    return response.data;
  }

  /**
   * 更新用户资料
   */
  static async updateUserProfile(data: UpdateUserProfileRequest): Promise<UserProfileResponse> {
    const response = await apiRequest.put<UserProfileResponse>('/users/profile', data);
    return response.data;
  }

  /**
   * 修改密码
   */
  static async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    const data: UpdateUserProfileRequest = {
      currentPassword,
      newPassword,
    };
    
    const response = await apiRequest.put<void>('/users/profile', data);
    return response.data;
  }

  /**
   * 更新用户名
   */
  static async updateUserName(name: string): Promise<UserProfileResponse> {
    const data: UpdateUserProfileRequest = {
      name,
    };
    
    const response = await apiRequest.put<UserProfileResponse>('/users/profile', data);
    return response.data;
  }

  /**
   * 验证当前密码
   */
  static async validateCurrentPassword(password: string): Promise<boolean> {
    try {
      // 这里可能需要后端提供专门的密码验证接口
      // 暂时通过尝试修改密码来验证
      await this.changePassword(password, password);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(): Promise<{
    totalTeams: number;
    createdTeams: number;
    joinedTeams: number;
    lastLoginTime: string;
  }> {
    // 这里可能需要后端提供专门的统计接口
    // 暂时返回模拟数据
    return {
      totalTeams: 0,
      createdTeams: 0,
      joinedTeams: 0,
      lastLoginTime: new Date().toISOString(),
    };
  }

  /**
   * 检查邮箱是否已被使用
   */
  static async checkEmailAvailable(email: string): Promise<boolean> {
    try {
      // 这里可能需要后端提供专门的检查接口
      // 暂时返回 true
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取用户活动日志
   */
  static async getUserActivityLog(params?: {
    startDate?: string;
    endDate?: string;
    limit?: number;
  }): Promise<Array<{
    id: number;
    action: string;
    description: string;
    timestamp: string;
    ipAddress?: string;
    userAgent?: string;
  }>> {
    // 这里需要后端提供活动日志接口
    // 暂时返回空数组
    return [];
  }

  /**
   * 导出用户数据
   */
  static async exportUserData(): Promise<Blob> {
    // 这里需要后端提供数据导出接口
    const response = await apiRequest.get('/users/export');
    return response as unknown as Blob;
  }

  /**
   * 删除用户账户
   */
  static async deleteAccount(password: string): Promise<void> {
    const response = await apiRequest.delete<void>('/users/account', {
      password,
    });
    return response.data;
  }

  /**
   * 获取用户偏好设置
   */
  static async getUserPreferences(): Promise<{
    language: string;
    timezone: string;
    theme: 'light' | 'dark' | 'auto';
    notifications: {
      email: boolean;
      push: boolean;
      teamInvites: boolean;
      systemUpdates: boolean;
    };
  }> {
    // 这里需要后端提供偏好设置接口
    // 暂时从本地存储获取
    const preferences = localStorage.getItem('user_preferences');
    if (preferences) {
      return JSON.parse(preferences);
    }
    
    // 默认设置
    return {
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      theme: 'light',
      notifications: {
        email: true,
        push: true,
        teamInvites: true,
        systemUpdates: false,
      },
    };
  }

  /**
   * 更新用户偏好设置
   */
  static async updateUserPreferences(preferences: {
    language?: string;
    timezone?: string;
    theme?: 'light' | 'dark' | 'auto';
    notifications?: {
      email?: boolean;
      push?: boolean;
      teamInvites?: boolean;
      systemUpdates?: boolean;
    };
  }): Promise<void> {
    // 这里需要后端提供偏好设置更新接口
    // 暂时保存到本地存储
    const currentPreferences = await this.getUserPreferences();
    const updatedPreferences = {
      ...currentPreferences,
      ...preferences,
      notifications: {
        ...currentPreferences.notifications,
        ...preferences.notifications,
      },
    };
    
    localStorage.setItem('user_preferences', JSON.stringify(updatedPreferences));
  }
}

// 导出默认实例
export default UserService;
