/**
 * API 相关类型定义
 * 基于后端 DTO 和 Entity 类生成
 */

// ============= 基础类型 =============

/**
 * API 响应基础结构
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
}

/**
 * 分页请求参数
 */
export interface PageRequest {
  current?: number;
  pageSize?: number;
}

/**
 * 分页响应结构
 */
export interface PageResponse<T> {
  list: T[];
  total: number;
  current: number;
  pageSize: number;
}

// ============= 认证相关类型 =============

/**
 * 登录请求
 */
export interface LoginRequest {
  email: string;
  password: string;
}

/**
 * 注册请求
 */
export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
}

/**
 * 用户信息
 */
export interface UserInfo {
  id: number;
  email: string;
  name: string;
}

/**
 * 团队信息（登录响应中的简化版本）
 */
export interface TeamInfo {
  id: number;
  name: string;
  isCreator: boolean;
  memberCount: number;
  lastAccessTime: string;
}

/**
 * 登录响应
 */
export interface LoginResponse {
  token: string;
  expiresIn: number;
  user: UserInfo;
  teams: TeamInfo[];
}

// ============= 团队管理相关类型 =============

/**
 * 创建团队请求
 */
export interface CreateTeamRequest {
  name: string;
  description?: string;
}

/**
 * 更新团队请求
 */
export interface UpdateTeamRequest {
  name: string;
  description?: string;
}

/**
 * 邀请成员请求
 */
export interface InviteMembersRequest {
  emails: string[];
}

/**
 * 团队详情响应
 */
export interface TeamDetailResponse {
  id: number;
  name: string;
  description?: string;
  createdBy: number;
  memberCount: number;
  isCreator: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 团队成员响应
 */
export interface TeamMemberResponse {
  id: number;
  accountId: number;
  email: string;
  name: string;
  isCreator: boolean;
  assignedAt: string;
  lastAccessTime: string;
  isActive: boolean;
}

// ============= 用户管理相关类型 =============

/**
 * 用户资料响应
 */
export interface UserProfileResponse {
  id: number;
  email: string;
  name: string;
  defaultSubscriptionPlanId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * 更新用户资料请求
 */
export interface UpdateUserProfileRequest {
  name?: string;
  currentPassword?: string;
  newPassword?: string;
}

// ============= 订阅管理相关类型 =============

/**
 * 订阅套餐响应
 */
export interface SubscriptionPlanResponse {
  id: number;
  name: string;
  description: string;
  maxSize: number;
  price: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 创建订阅请求
 */
export interface CreateSubscriptionRequest {
  planId: number;
  duration: number;
}

/**
 * 订阅状态枚举
 */
export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED'
}

/**
 * 订阅响应
 */
export interface SubscriptionResponse {
  id: number;
  accountId: number;
  subscriptionPlanId: number;
  planName: string;
  planDescription: string;
  maxSize: number;
  price: number;
  startDate: string;
  endDate: string;
  status: SubscriptionStatus;
  createdAt: string;
  updatedAt: string;
}

// ============= 实体类型 =============

/**
 * 账户实体
 */
export interface Account {
  id: number;
  email: string;
  name: string;
  defaultSubscriptionPlanId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * 团队实体
 */
export interface Team {
  id: number;
  name: string;
  description?: string;
  createdBy: number;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 团队成员实体
 */
export interface TeamMember {
  id: number;
  teamId: number;
  accountId: number;
  isCreator: boolean;
  assignedAt: string;
  lastAccessTime: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 订阅套餐实体
 */
export interface SubscriptionPlan {
  id: number;
  name: string;
  description: string;
  maxSize: number;
  price: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 账户订阅实体
 */
export interface AccountSubscription {
  id: number;
  accountId: number;
  subscriptionPlanId: number;
  startDate: string;
  endDate: string;
  status: SubscriptionStatus;
  createdAt: string;
  updatedAt: string;
}

/**
 * 账户关系实体
 */
export interface AccountRelation {
  id: number;
  accountId: number;
  invitedBy: number;
  invitedAt: string;
  isActive: boolean;
  isDeleted: boolean;
  updatedAt: string;
}
