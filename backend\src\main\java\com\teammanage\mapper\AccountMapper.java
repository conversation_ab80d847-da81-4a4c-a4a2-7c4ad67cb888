package com.teammanage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teammanage.entity.Account;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户账户Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AccountMapper extends BaseMapper<Account> {

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM account WHERE email = #{email}")
    Account findByEmail(@Param("email") String email);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) FROM account WHERE email = #{email}")
    boolean existsByEmail(@Param("email") String email);

}
