package com.teammanage.service;

import com.teammanage.dto.request.CreateSubscriptionRequest;
import com.teammanage.dto.response.SubscriptionPlanResponse;
import com.teammanage.dto.response.SubscriptionResponse;
import com.teammanage.entity.AccountSubscription;
import com.teammanage.entity.SubscriptionPlan;
import com.teammanage.exception.BusinessException;
import com.teammanage.exception.ResourceNotFoundException;
import com.teammanage.mapper.AccountSubscriptionMapper;
import com.teammanage.mapper.SubscriptionPlanMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订阅管理服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class SubscriptionService {

    private static final Logger log = LoggerFactory.getLogger(SubscriptionService.class);

    @Autowired
    private SubscriptionPlanMapper subscriptionPlanMapper;

    @Autowired
    private AccountSubscriptionMapper accountSubscriptionMapper;

    @Autowired
    private UserService userService;

    /**
     * 获取所有可用的订阅套餐
     * 
     * @return 套餐列表
     */
    public List<SubscriptionPlanResponse> getAllPlans() {
        List<SubscriptionPlan> plans = subscriptionPlanMapper.findAllActive();
        
        return plans.stream()
                .map(this::convertToPlanResponse)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID获取订阅套餐
     * 
     * @param planId 套餐ID
     * @return 套餐信息
     */
    public SubscriptionPlanResponse getPlanById(Long planId) {
        SubscriptionPlan plan = subscriptionPlanMapper.selectById(planId);
        if (plan == null || !plan.getIsActive()) {
            throw new ResourceNotFoundException("订阅套餐不存在或已停用");
        }
        
        return convertToPlanResponse(plan);
    }

    /**
     * 创建订阅
     * 
     * @param request 创建订阅请求
     * @param userId 用户ID
     * @return 订阅信息
     */
    @Transactional
    public SubscriptionResponse createSubscription(CreateSubscriptionRequest request, Long userId) {
        // 验证套餐是否存在
        SubscriptionPlan plan = subscriptionPlanMapper.selectById(request.getPlanId());
        if (plan == null || !plan.getIsActive()) {
            throw new BusinessException("订阅套餐不存在或已停用");
        }

        // 检查是否已有有效订阅
        AccountSubscription currentSubscription = accountSubscriptionMapper.findCurrentActiveByAccountId(userId);
        if (currentSubscription != null) {
            // 取消当前订阅
            currentSubscription.setStatus(AccountSubscription.SubscriptionStatus.CANCELED);
            accountSubscriptionMapper.updateById(currentSubscription);
            log.info("取消用户当前订阅: userId={}, subscriptionId={}", userId, currentSubscription.getId());
        }

        // 创建新订阅
        AccountSubscription subscription = new AccountSubscription();
        subscription.setAccountId(userId);
        subscription.setSubscriptionPlanId(request.getPlanId());
        subscription.setStartDate(LocalDate.now());
        
        // 计算结束日期
        if (request.getDuration() != null && request.getDuration() > 0) {
            subscription.setEndDate(LocalDate.now().plusMonths(request.getDuration()));
        }
        
        subscription.setStatus(AccountSubscription.SubscriptionStatus.ACTIVE);
        
        accountSubscriptionMapper.insert(subscription);

        // 更新用户默认套餐
        userService.updateDefaultSubscriptionPlan(userId, request.getPlanId());

        log.info("创建订阅成功: userId={}, planId={}, duration={}", 
                userId, request.getPlanId(), request.getDuration());

        return convertToSubscriptionResponse(subscription, plan);
    }

    /**
     * 获取用户的订阅历史
     * 
     * @param userId 用户ID
     * @return 订阅历史列表
     */
    public List<SubscriptionResponse> getUserSubscriptions(Long userId) {
        List<AccountSubscription> subscriptions = accountSubscriptionMapper.findByAccountId(userId);
        
        return subscriptions.stream()
                .map(subscription -> {
                    SubscriptionPlan plan = subscriptionPlanMapper.selectById(subscription.getSubscriptionPlanId());
                    return convertToSubscriptionResponse(subscription, plan);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取用户当前有效订阅
     * 
     * @param userId 用户ID
     * @return 当前订阅信息
     */
    public SubscriptionResponse getCurrentSubscription(Long userId) {
        AccountSubscription subscription = accountSubscriptionMapper.findCurrentActiveByAccountId(userId);
        if (subscription == null) {
            return null;
        }
        
        SubscriptionPlan plan = subscriptionPlanMapper.selectById(subscription.getSubscriptionPlanId());
        return convertToSubscriptionResponse(subscription, plan);
    }

    /**
     * 取消订阅
     * 
     * @param subscriptionId 订阅ID
     * @param userId 用户ID
     */
    @Transactional
    public void cancelSubscription(Long subscriptionId, Long userId) {
        AccountSubscription subscription = accountSubscriptionMapper.selectById(subscriptionId);
        if (subscription == null || !subscription.getAccountId().equals(userId)) {
            throw new ResourceNotFoundException("订阅不存在");
        }

        if (subscription.getStatus() != AccountSubscription.SubscriptionStatus.ACTIVE) {
            throw new BusinessException("订阅状态不允许取消");
        }

        subscription.setStatus(AccountSubscription.SubscriptionStatus.CANCELED);
        accountSubscriptionMapper.updateById(subscription);

        log.info("取消订阅成功: userId={}, subscriptionId={}", userId, subscriptionId);
    }

    /**
     * 检查用户是否有有效订阅
     * 
     * @param userId 用户ID
     * @return 是否有有效订阅
     */
    public boolean hasActiveSubscription(Long userId) {
        AccountSubscription subscription = accountSubscriptionMapper.findCurrentActiveByAccountId(userId);
        return subscription != null;
    }

    /**
     * 获取用户的数据限制
     * 
     * @param userId 用户ID
     * @return 数据限制数量
     */
    public int getUserDataLimit(Long userId) {
        AccountSubscription subscription = accountSubscriptionMapper.findCurrentActiveByAccountId(userId);
        if (subscription != null) {
            SubscriptionPlan plan = subscriptionPlanMapper.selectById(subscription.getSubscriptionPlanId());
            if (plan != null) {
                return plan.getMaxSize();
            }
        }
        
        // 默认返回免费套餐限制
        return 100;
    }

    /**
     * 转换套餐实体为响应DTO
     */
    private SubscriptionPlanResponse convertToPlanResponse(SubscriptionPlan plan) {
        SubscriptionPlanResponse response = new SubscriptionPlanResponse();
        response.setId(plan.getId());
        response.setName(plan.getName());
        response.setDescription(plan.getDescription());
        response.setMaxSize(plan.getMaxSize());
        response.setPrice(plan.getPrice());
        response.setIsActive(plan.getIsActive());
        response.setCreatedAt(plan.getCreatedAt());
        response.setUpdatedAt(plan.getUpdatedAt());
        
        return response;
    }

    /**
     * 转换订阅实体为响应DTO
     */
    private SubscriptionResponse convertToSubscriptionResponse(AccountSubscription subscription, SubscriptionPlan plan) {
        SubscriptionResponse response = new SubscriptionResponse();
        response.setId(subscription.getId());
        response.setAccountId(subscription.getAccountId());
        response.setSubscriptionPlanId(subscription.getSubscriptionPlanId());
        response.setStartDate(subscription.getStartDate());
        response.setEndDate(subscription.getEndDate());
        response.setStatus(subscription.getStatus());
        response.setCreatedAt(subscription.getCreatedAt());
        response.setUpdatedAt(subscription.getUpdatedAt());
        
        if (plan != null) {
            response.setPlanName(plan.getName());
            response.setPlanDescription(plan.getDescription());
            response.setMaxSize(plan.getMaxSize());
            response.setPrice(plan.getPrice());
        }
        
        return response;
    }

}
