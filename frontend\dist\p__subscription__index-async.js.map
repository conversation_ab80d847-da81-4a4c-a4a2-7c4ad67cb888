{"version": 3, "sources": ["src/pages/subscription/index.tsx"], "sourcesContent": ["/**\n * 订阅管理页面 - 合并订阅详情和套餐选择\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Tabs, \n  Typography, \n  Space,\n  message\n} from 'antd';\nimport {\n  CrownOutlined,\n  ShoppingOutlined\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { SubscriptionService } from '@/services';\nimport type { SubscriptionResponse } from '@/types/api';\n\n// 导入原有的组件内容\nimport SubscriptionManageContent from './components/SubscriptionManageContent';\nimport SubscriptionPlansContent from './components/SubscriptionPlansContent';\n\nconst { Title } = Typography;\n\nconst SubscriptionPage: React.FC = () => {\n  const [activeTab, setActiveTab] = useState('manage');\n  const [currentSubscription, setCurrentSubscription] = useState<SubscriptionResponse | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchCurrentSubscription();\n  }, []);\n\n  const fetchCurrentSubscription = async () => {\n    try {\n      setLoading(true);\n      const subscription = await SubscriptionService.getCurrentSubscription();\n      setCurrentSubscription(subscription);\n      // 如果有当前订阅，默认显示管理页面；否则显示套餐选择页面\n      if (subscription) {\n        setActiveTab('manage');\n      } else {\n        setActiveTab('plans');\n      }\n    } catch (error) {\n      console.error('获取当前订阅失败:', error);\n      // 如果没有订阅，默认显示套餐选择页面\n      setActiveTab('plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubscriptionChange = () => {\n    // 订阅状态发生变化时，重新获取当前订阅信息\n    fetchCurrentSubscription();\n  };\n\n  const tabItems = [\n    {\n      key: 'manage',\n      label: (\n        <Space>\n          <CrownOutlined />\n          订阅详情\n        </Space>\n      ),\n      children: (\n        <SubscriptionManageContent \n          currentSubscription={currentSubscription}\n          loading={loading}\n          onRefresh={fetchCurrentSubscription}\n        />\n      )\n    },\n    {\n      key: 'plans',\n      label: (\n        <Space>\n          <ShoppingOutlined />\n          套餐选择\n        </Space>\n      ),\n      children: (\n        <SubscriptionPlansContent \n          onSubscriptionSuccess={handleSubscriptionChange}\n        />\n      )\n    }\n  ];\n\n  return (\n    <PageContainer title=\"订阅管理\">\n      <Card>\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={tabItems}\n          size=\"large\"\n        />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default SubscriptionPage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BAyGD;;;eAAA;;;;;;;wEAvG2C;6BAOpC;8BAIA;sCACuB;iCACM;2FAIE;0FACD;;;;;;;;;;AAErC,MAAM,EAAE,KAAK,EAAE,GAAG,gBAAU;AAE5B,MAAM,mBAA6B;;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAA8B;IAC5F,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IAEvC,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,2BAA2B;QAC/B,IAAI;YACF,WAAW;YACX,MAAM,eAAe,MAAM,6BAAmB,CAAC,sBAAsB;YACrE,uBAAuB;YACvB,8BAA8B;YAC9B,IAAI,cACF,aAAa;iBAEb,aAAa;QAEjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,oBAAoB;YACpB,aAAa;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,2BAA2B;QAC/B,uBAAuB;QACvB;IACF;IAEA,MAAM,WAAW;QACf;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,oBAAa;;;;;oBAAG;;;;;;;YAIrB,wBACE,2BAAC,kCAAyB;gBACxB,qBAAqB;gBACrB,SAAS;gBACT,WAAW;;;;;;QAGjB;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,uBAAgB;;;;;oBAAG;;;;;;;YAIxB,wBACE,2BAAC,iCAAwB;gBACvB,uBAAuB;;;;;;QAG7B;KACD;IAED,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,MAAK;;;;;;;;;;;;;;;;AAKf;GA/EM;KAAA;IAiFN,WAAe"}