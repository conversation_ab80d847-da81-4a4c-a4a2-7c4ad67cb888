-- 团队管理系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS team_manage DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE team_manage;

-- 套餐表 (subscription_plan)
CREATE TABLE subscription_plan (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '套餐ID',
  name VARCHAR(50) NOT NULL COMMENT '套餐名称',
  description TEXT COMMENT '套餐说明',
  max_size INT NOT NULL COMMENT '数据数量上限',
  price DECIMAL(10,2) NOT NULL COMMENT '价格(元/月)',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_active (is_active),
  INDEX idx_price (price)
) COMMENT='产品套餐表';

-- 用户账户表 (account)
CREATE TABLE account (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
  email VARCHAR(255) NOT NULL UNIQUE COMMENT '邮箱',
  password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
  name VARCHAR(100) NOT NULL COMMENT '用户名',
  default_subscription_plan_id BIGINT COMMENT '当前套餐ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_email (email),
  INDEX idx_subscription_plan (default_subscription_plan_id),
  FOREIGN KEY (default_subscription_plan_id) REFERENCES subscription_plan(id)
) COMMENT='用户账户表';

-- 用户订阅记录表 (account_subscription)
CREATE TABLE account_subscription (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
  account_id BIGINT NOT NULL COMMENT '用户ID',
  subscription_plan_id BIGINT NOT NULL COMMENT '套餐ID',
  start_date DATE NOT NULL COMMENT '开始日期',
  end_date DATE COMMENT '结束日期',
  status ENUM('ACTIVE', 'EXPIRED', 'CANCELED') DEFAULT 'ACTIVE' COMMENT '订阅状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_account_status (account_id, status),
  INDEX idx_date_range (start_date, end_date),
  INDEX idx_plan (subscription_plan_id),
  FOREIGN KEY (account_id) REFERENCES account(id),
  FOREIGN KEY (subscription_plan_id) REFERENCES subscription_plan(id)
) COMMENT='用户订阅历史表';

-- 账号邀请关系表 (account_relation)
CREATE TABLE account_relation (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关系ID',
  account_id BIGINT NOT NULL COMMENT '被邀请账号ID',
  invited_by BIGINT NOT NULL COMMENT '邀请账号ID',
  invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '邀请时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_deleted BOOLEAN DEFAULT FALSE COMMENT '删除标记',
  
  INDEX idx_account_active (account_id, is_active, is_deleted),
  INDEX idx_inviter (invited_by, is_deleted),
  INDEX idx_relation (account_id, invited_by),
  FOREIGN KEY (account_id) REFERENCES account(id),
  FOREIGN KEY (invited_by) REFERENCES account(id),
  UNIQUE KEY uk_relation (account_id, invited_by, is_deleted)
) COMMENT='账号邀请关系表';

-- 团队信息表 (team)
CREATE TABLE team (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '团队ID',
  name VARCHAR(100) NOT NULL COMMENT '团队名称',
  description TEXT COMMENT '团队描述',
  created_by BIGINT NOT NULL COMMENT '创建人ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_deleted BOOLEAN DEFAULT FALSE COMMENT '删除标记',

  INDEX idx_creator (created_by, is_deleted),
  INDEX idx_name (name),
  FOREIGN KEY (created_by) REFERENCES account(id)
) COMMENT='团队信息表';

-- 团队成员表 (team_member)
CREATE TABLE team_member (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '团队成员ID',
  team_id BIGINT NOT NULL COMMENT '团队ID',
  account_id BIGINT NOT NULL COMMENT '用户ID',
  is_creator BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为创建者',
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  last_access_time TIMESTAMP NULL COMMENT '最后访问时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '账号状态',
  is_deleted BOOLEAN DEFAULT FALSE COMMENT '删除标记',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  INDEX idx_team_active (team_id, is_active, is_deleted),
  INDEX idx_account_team (account_id, team_id, is_deleted),
  INDEX idx_creator (team_id, is_creator, is_deleted),
  INDEX idx_last_access (account_id, last_access_time),
  FOREIGN KEY (team_id) REFERENCES team(id),
  FOREIGN KEY (account_id) REFERENCES account(id),
  UNIQUE KEY uk_team_member (team_id, account_id, is_deleted)
) COMMENT='团队成员表';



-- 插入默认套餐数据
INSERT INTO subscription_plan (name, description, max_size, price, is_active) VALUES
('免费版', '适合个人用户和小团队', 100, 0.00, TRUE),
('标准版', '适合中小企业', 1000, 99.00, TRUE),
('专业版', '适合大型企业', 10000, 299.00, TRUE),
('企业版', '无限制使用', 999999, 999.00, TRUE);
