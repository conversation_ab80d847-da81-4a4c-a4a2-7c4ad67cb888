package com.teammanage.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.teammanage.dto.request.LoginRequest;
import com.teammanage.dto.request.RegisterRequest;
import com.teammanage.dto.response.LoginResponse;
import com.teammanage.entity.Account;
import com.teammanage.entity.Team;
import com.teammanage.entity.TeamMember;
import com.teammanage.exception.BusinessException;
import com.teammanage.exception.ResourceNotFoundException;
import com.teammanage.mapper.AccountMapper;
import com.teammanage.mapper.TeamMapper;
import com.teammanage.mapper.TeamMemberMapper;
import com.teammanage.util.JwtTokenUtil;
import com.teammanage.util.PasswordUtil;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 认证服务
 * 实现单阶段登录机制，支持团队上下文切换
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class AuthService {

    private static final Logger log = LoggerFactory.getLogger(AuthService.class);

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private TeamMapper teamMapper;

    @Autowired
    private TeamMemberMapper teamMemberMapper;

    @Autowired
    private UserSessionService userSessionService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private PasswordUtil passwordUtil;

    /**
     * 用户注册
     * 
     * @param request 注册请求
     * @return 注册结果
     */
    @Transactional
    public void register(RegisterRequest request) {
        // 检查邮箱是否已存在
        if (accountMapper.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已被注册");
        }

        // 创建用户账户
        Account account = new Account();
        account.setEmail(request.getEmail());
        account.setName(request.getName());
        account.setPasswordHash(passwordUtil.encode(request.getPassword()));
        account.setDefaultSubscriptionPlanId(1L); // 默认免费套餐

        accountMapper.insert(account);
        log.info("用户注册成功: email={}, name={}", request.getEmail(), request.getName());
    }

    /**
     * 用户登录（单阶段）
     *
     * @param request 登录请求
     * @param httpRequest HTTP请求
     * @return 登录响应
     */
    @Transactional
    public LoginResponse login(LoginRequest request, HttpServletRequest httpRequest) {
        // 验证用户凭据
        Account account = accountMapper.findByEmail(request.getEmail());
        if (account == null) {
            throw new BusinessException("邮箱或密码错误");
        }

        if (!passwordUtil.matches(request.getPassword(), account.getPasswordHash())) {
            throw new BusinessException("邮箱或密码错误");
        }

        // 生成用户Token
        String userToken = jwtTokenUtil.generateToken(account);
        String jti = jwtTokenUtil.getJtiFromToken(userToken);

        // 创建会话记录
        com.teammanage.model.SessionInfo sessionInfo = com.teammanage.model.SessionInfo.builder()
                .accountId(account.getId())
                .tokenHash(jti)
                .deviceInfo(getDeviceInfo(httpRequest))
                .ipAddress(getClientIpAddress(httpRequest))
                .userAgent(httpRequest.getHeader("User-Agent"))
                .loginTime(LocalDateTime.now())
                .lastActivityTime(LocalDateTime.now())
                .isActive(true)
                .build();

        userSessionService.createSession(sessionInfo);

        // 查询用户的团队列表
        List<TeamMember> teamMembers = teamMemberMapper.findByAccountId(account.getId());
        List<LoginResponse.TeamInfo> teams = teamMembers.stream()
                .map(this::convertToTeamInfo)
                .collect(Collectors.toList());

        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setToken(userToken); // 返回用户Token
        response.setExpiresIn(jwtTokenUtil.getExpirationDateFromToken(userToken).getTime() - System.currentTimeMillis());

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(account.getId());
        userInfo.setEmail(account.getEmail());
        userInfo.setName(account.getName());
        response.setUser(userInfo);

        response.setTeams(teams);

        log.info("用户登录成功: userId={}, email={}, teamCount={}",
                account.getId(), account.getEmail(), teams.size());

        return response;
    }



    /**
     * 刷新Token
     *
     * @param currentToken 当前Token
     * @return 新的Token信息
     */
    @Transactional
    public LoginResponse refreshToken(String currentToken) {
        // 验证当前Token
        if (!jwtTokenUtil.validateToken(currentToken)) {
            throw new BusinessException("Token无效或已过期");
        }

        Long userId = jwtTokenUtil.getUserIdFromToken(currentToken);
        String currentJti = jwtTokenUtil.getJtiFromToken(currentToken);

        // 查找当前会话
        com.teammanage.model.SessionInfo session = userSessionService.findByTokenHash(currentJti);
        if (session == null) {
            throw new BusinessException("未找到有效的会话");
        }

        // 重新生成用户Token
        Account account = accountMapper.selectById(userId);
        if (account == null) {
            throw new ResourceNotFoundException("用户不存在");
        }

        String newToken = jwtTokenUtil.generateToken(account);
        String newJti = jwtTokenUtil.getJtiFromToken(newToken);

        // 更新会话记录
        userSessionService.invalidateSession(currentJti);
        session.setTokenHash(newJti);
        session.setLastActivityTime(LocalDateTime.now());
        userSessionService.createSession(session);

        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setToken(newToken);
        response.setExpiresIn(jwtTokenUtil.getExpirationDateFromToken(newToken).getTime() - System.currentTimeMillis());

        log.info("用户Token刷新成功: userId={}", userId);
        return response;
    }



    /**
     * 选择团队
     *
     * @param teamId 团队ID
     * @param currentToken 当前Token
     * @return 包含团队信息的新Token
     */
    @Transactional
    public LoginResponse selectTeam(Long teamId, String currentToken) {
        // 验证当前Token
        if (!jwtTokenUtil.validateToken(currentToken)) {
            throw new BusinessException("Token无效或已过期");
        }

        Long userId = jwtTokenUtil.getUserIdFromToken(currentToken);

        // 验证用户是否为该团队成员
        TeamMember teamMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, userId);
        if (teamMember == null || !teamMember.getIsActive()) {
            throw new BusinessException("您不是该团队的成员或成员状态无效");
        }

        // 获取用户信息
        Account account = accountMapper.selectById(userId);
        if (account == null) {
            throw new ResourceNotFoundException("用户不存在");
        }

        // 生成包含团队信息的新Token
        String newToken = jwtTokenUtil.generateTokenWithTeam(account, teamId, teamMember.getIsCreator());
        String newJti = jwtTokenUtil.getJtiFromToken(newToken);

        // 更新会话信息
        String currentJti = jwtTokenUtil.getJtiFromToken(currentToken);
        userSessionService.invalidateSession(currentJti); // 使旧Token失效

        // 创建新会话
        com.teammanage.model.SessionInfo sessionInfo = com.teammanage.model.SessionInfo.builder()
                .accountId(account.getId())
                .tokenHash(newJti)
                .deviceInfo("") // 可以从请求中获取
                .ipAddress("") // 可以从请求中获取
                .userAgent("") // 可以从请求中获取
                .loginTime(LocalDateTime.now())
                .lastActivityTime(LocalDateTime.now())
                .isActive(true)
                .build();

        userSessionService.createSession(sessionInfo);

        // 更新团队成员的最后访问时间
        teamMember.setLastAccessTime(LocalDateTime.now());
        teamMemberMapper.updateById(teamMember);

        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setToken(newToken);
        response.setExpiresIn(jwtTokenUtil.getExpirationDateFromToken(newToken).getTime() - System.currentTimeMillis());

        LoginResponse.TeamInfo teamInfo = new LoginResponse.TeamInfo();
        teamInfo.setId(teamId);
        teamInfo.setIsCreator(teamMember.getIsCreator());
        response.setTeam(teamInfo);

        log.info("团队选择成功: userId={}, teamId={}, isCreator={}",
                userId, teamId, teamMember.getIsCreator());

        return response;
    }

    /**
     * 切换团队
     *
     * @param teamId 团队ID
     * @param currentToken 当前Token
     * @return 包含新团队信息的Token
     */
    @Transactional
    public LoginResponse switchTeam(Long teamId, String currentToken) {
        return selectTeam(teamId, currentToken); // 切换团队与选择团队逻辑相同
    }

    /**
     * 清除团队上下文
     *
     * @param currentToken 当前Token
     * @return 不包含团队信息的用户Token
     */
    @Transactional
    public String clearTeam(String currentToken) {
        // 验证当前Token
        if (!jwtTokenUtil.validateToken(currentToken)) {
            throw new BusinessException("Token无效或已过期");
        }

        Long userId = jwtTokenUtil.getUserIdFromToken(currentToken);

        // 获取用户信息
        Account account = accountMapper.selectById(userId);
        if (account == null) {
            throw new ResourceNotFoundException("用户不存在");
        }

        // 生成不包含团队信息的新Token
        String newToken = jwtTokenUtil.generateToken(account);
        String newJti = jwtTokenUtil.getJtiFromToken(newToken);

        // 更新会话信息
        String currentJti = jwtTokenUtil.getJtiFromToken(currentToken);
        userSessionService.invalidateSession(currentJti); // 使旧Token失效

        // 创建新会话
        com.teammanage.model.SessionInfo sessionInfo = com.teammanage.model.SessionInfo.builder()
                .accountId(account.getId())
                .tokenHash(newJti)
                .deviceInfo("") // 可以从请求中获取
                .ipAddress("") // 可以从请求中获取
                .userAgent("") // 可以从请求中获取
                .loginTime(LocalDateTime.now())
                .lastActivityTime(LocalDateTime.now())
                .isActive(true)
                .build();

        userSessionService.createSession(sessionInfo);

        log.info("团队上下文已清除: userId={}", userId);

        return newToken;
    }

    /**
     * 登出
     *
     * @param token 当前Token
     */
    @Transactional
    public void logout(String token) {
        if (StringUtils.hasText(token)) {
            String jti = jwtTokenUtil.getJtiFromToken(token);
            userSessionService.invalidateSession(jti);
            log.info("用户登出成功: jti={}", jti);
        }
    }

    /**
     * 转换团队成员信息为响应格式
     */
    private LoginResponse.TeamInfo convertToTeamInfo(TeamMember teamMember) {
        LoginResponse.TeamInfo teamInfo = new LoginResponse.TeamInfo();
        teamInfo.setId(teamMember.getTeamId());
        teamInfo.setIsCreator(teamMember.getIsCreator());
        teamInfo.setLastAccessTime(teamMember.getLastAccessTime());

        // 查询团队详细信息
        Team team = teamMapper.selectById(teamMember.getTeamId());
        if (team != null && !team.getIsDeleted()) {
            teamInfo.setName(team.getName());
            // 统计团队成员数量
            int memberCount = teamMemberMapper.countByTeamId(teamMember.getTeamId());
            teamInfo.setMemberCount(memberCount);
        }

        return teamInfo;
    }

    /**
     * 获取设备信息
     */
    private String getDeviceInfo(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        // 简单的设备信息提取，实际项目中可以使用更复杂的解析
        return userAgent != null ? userAgent.substring(0, Math.min(userAgent.length(), 500)) : "Unknown";
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

}
