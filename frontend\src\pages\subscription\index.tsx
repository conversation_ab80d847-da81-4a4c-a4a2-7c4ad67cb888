/**
 * 订阅管理页面 - 合并订阅详情和套餐选择
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Typography, 
  Space,
  message
} from 'antd';
import {
  CrownOutlined,
  ShoppingOutlined
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { SubscriptionService } from '@/services';
import type { SubscriptionResponse } from '@/types/api';

// 导入原有的组件内容
import SubscriptionManageContent from './components/SubscriptionManageContent';
import SubscriptionPlansContent from './components/SubscriptionPlansContent';

const { Title } = Typography;

const SubscriptionPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('manage');
  const [currentSubscription, setCurrentSubscription] = useState<SubscriptionResponse | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCurrentSubscription();
  }, []);

  const fetchCurrentSubscription = async () => {
    try {
      setLoading(true);
      const subscription = await SubscriptionService.getCurrentSubscription();
      setCurrentSubscription(subscription);
      // 如果有当前订阅，默认显示管理页面；否则显示套餐选择页面
      if (subscription) {
        setActiveTab('manage');
      } else {
        setActiveTab('plans');
      }
    } catch (error) {
      console.error('获取当前订阅失败:', error);
      // 如果没有订阅，默认显示套餐选择页面
      setActiveTab('plans');
    } finally {
      setLoading(false);
    }
  };

  const handleSubscriptionChange = () => {
    // 订阅状态发生变化时，重新获取当前订阅信息
    fetchCurrentSubscription();
  };

  const tabItems = [
    {
      key: 'manage',
      label: (
        <Space>
          <CrownOutlined />
          订阅详情
        </Space>
      ),
      children: (
        <SubscriptionManageContent 
          currentSubscription={currentSubscription}
          loading={loading}
          onRefresh={fetchCurrentSubscription}
        />
      )
    },
    {
      key: 'plans',
      label: (
        <Space>
          <ShoppingOutlined />
          套餐选择
        </Space>
      ),
      children: (
        <SubscriptionPlansContent 
          onSubscriptionSuccess={handleSubscriptionChange}
        />
      )
    }
  ];

  return (
    <PageContainer title="订阅管理">
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </Card>
    </PageContainer>
  );
};

export default SubscriptionPage;
