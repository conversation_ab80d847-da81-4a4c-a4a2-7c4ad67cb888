((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__team__index'],
{ "src/pages/team/index.tsx": function (module, exports, __mako_require__){
/**
 * 团队管理页面 - 合并团队详情和团队列表
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _services = __mako_require__("src/services/index.ts");
var _TeamDetailContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamDetailContent.tsx"));
var _TeamListContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/components/TeamListContent.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title } = _antd.Typography;
const TeamManagePage = ()=>{
    _s();
    const [activeTab, setActiveTab] = (0, _react.useState)('detail');
    const [loading, setLoading] = (0, _react.useState)(true);
    const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
    const { initialState } = (0, _max.useModel)('@@initialState');
    (0, _react.useEffect)(()=>{
        // 如果有当前团队，默认显示团队详情；否则显示团队列表
        if (initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam) {
            setActiveTab('detail');
            fetchTeamDetail();
        } else {
            setActiveTab('list');
            setLoading(false);
        }
    }, [
        initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam
    ]);
    const fetchTeamDetail = async ()=>{
        try {
            setLoading(true);
            const detail = await _services.TeamService.getCurrentTeamDetail();
            setTeamDetail(detail);
        } catch (error) {
            console.error('获取团队详情失败:', error);
            _antd.message.error('获取团队详情失败');
        } finally{
            setLoading(false);
        }
    };
    const handleCreateTeam = ()=>{
        _max.history.push('/team/create');
    };
    const handleTabChange = (key)=>{
        setActiveTab(key);
        if (key === 'detail' && !teamDetail && (initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam)) fetchTeamDetail();
    };
    const tabItems = [
        {
            key: 'detail',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/index.tsx",
                        lineNumber: 76,
                        columnNumber: 11
                    }, this),
                    "当前团队"
                ]
            }, void 0, true, {
                fileName: "src/pages/team/index.tsx",
                lineNumber: 75,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamDetailContent.default, {
                teamDetail: teamDetail,
                loading: loading,
                onRefresh: fetchTeamDetail
            }, void 0, false, {
                fileName: "src/pages/team/index.tsx",
                lineNumber: 81,
                columnNumber: 9
            }, this),
            disabled: !(initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam)
        },
        {
            key: 'list',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UnorderedListOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/index.tsx",
                        lineNumber: 93,
                        columnNumber: 11
                    }, this),
                    "我的团队"
                ]
            }, void 0, true, {
                fileName: "src/pages/team/index.tsx",
                lineNumber: 92,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamListContent.default, {}, void 0, false, {
                fileName: "src/pages/team/index.tsx",
                lineNumber: 97,
                columnNumber: 17
            }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "团队管理",
        extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
            type: "primary",
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                fileName: "src/pages/team/index.tsx",
                lineNumber: 107,
                columnNumber: 17
            }, void 0),
            onClick: handleCreateTeam,
            children: "创建团队"
        }, void 0, false, {
            fileName: "src/pages/team/index.tsx",
            lineNumber: 105,
            columnNumber: 9
        }, void 0),
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: handleTabChange,
                items: tabItems,
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/team/index.tsx",
                lineNumber: 115,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "src/pages/team/index.tsx",
            lineNumber: 114,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/index.tsx",
        lineNumber: 102,
        columnNumber: 5
    }, this);
};
_s(TeamManagePage, "vvG/pm4RWxgbbYupgFb3hBy40+Y=", false, function() {
    return [
        _max.useModel
    ];
});
_c = TeamManagePage;
var _default = TeamManagePage;
var _c;
$RefreshReg$(_c, "TeamManagePage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__team__index-async.js.map