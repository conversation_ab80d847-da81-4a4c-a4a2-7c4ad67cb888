package com.teammanage.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.teammanage.common.ApiResponse;
import com.teammanage.dto.request.UpdateUserProfileRequest;
import com.teammanage.dto.response.UserProfileResponse;
import com.teammanage.service.UserService;
import com.teammanage.util.SecurityUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/users")
@Tag(name = "用户管理", description = "用户信息管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class UserController {

    private static final Logger log = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户资料
     */
    @GetMapping("/profile")
    @Operation(summary = "获取用户资料", description = "获取当前用户的个人资料")
    public ApiResponse<UserProfileResponse> getUserProfile() {
        Long userId = SecurityUtil.getCurrentUserId();
        UserProfileResponse response = userService.getUserProfile(userId);
        return ApiResponse.success(response);
    }

    /**
     * 更新用户资料
     */
    @PostMapping("/profile/update")
    @Operation(summary = "更新用户资料", description = "更新当前用户的个人资料")
    public ApiResponse<UserProfileResponse> updateUserProfile(@Valid @RequestBody UpdateUserProfileRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        UserProfileResponse response = userService.updateUserProfile(userId, request);
        return ApiResponse.success("用户资料更新成功", response);
    }

}
