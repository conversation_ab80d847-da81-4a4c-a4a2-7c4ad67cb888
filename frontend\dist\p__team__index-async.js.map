{"version": 3, "sources": ["src/pages/team/index.tsx"], "sourcesContent": ["/**\n * 团队管理页面 - 合并团队详情和团队列表\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Tabs, \n  Typography, \n  message,\n  Button,\n  Space\n} from 'antd';\nimport {\n  TeamOutlined,\n  UnorderedListOutlined,\n  PlusOutlined\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { history, useModel } from '@umijs/max';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\n\n// 导入原有的组件\nimport TeamDetailContent from './detail/components/TeamDetailContent';\nimport TeamListContent from './components/TeamListContent';\n\nconst { Title } = Typography;\n\nconst TeamManagePage: React.FC = () => {\n  const [activeTab, setActiveTab] = useState('detail');\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const { initialState } = useModel('@@initialState');\n\n  useEffect(() => {\n    // 如果有当前团队，默认显示团队详情；否则显示团队列表\n    if (initialState?.currentTeam) {\n      setActiveTab('detail');\n      fetchTeamDetail();\n    } else {\n      setActiveTab('list');\n      setLoading(false);\n    }\n  }, [initialState?.currentTeam]);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      message.error('获取团队详情失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateTeam = () => {\n    history.push('/team/create');\n  };\n\n  const handleTabChange = (key: string) => {\n    setActiveTab(key);\n    if (key === 'detail' && !teamDetail && initialState?.currentTeam) {\n      fetchTeamDetail();\n    }\n  };\n\n  const tabItems = [\n    {\n      key: 'detail',\n      label: (\n        <Space>\n          <TeamOutlined />\n          当前团队\n        </Space>\n      ),\n      children: (\n        <TeamDetailContent \n          teamDetail={teamDetail}\n          loading={loading}\n          onRefresh={fetchTeamDetail}\n        />\n      ),\n      disabled: !initialState?.currentTeam\n    },\n    {\n      key: 'list',\n      label: (\n        <Space>\n          <UnorderedListOutlined />\n          我的团队\n        </Space>\n      ),\n      children: <TeamListContent />\n    }\n  ];\n\n  return (\n    <PageContainer\n      title=\"团队管理\"\n      extra={\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleCreateTeam}\n        >\n          创建团队\n        </Button>\n      }\n    >\n      <Card>\n        <Tabs\n          activeKey={activeTab}\n          onChange={handleTabChange}\n          items={tabItems}\n          size=\"large\"\n        />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default TeamManagePage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA2HD;;;eAAA;;;;;;;wEAzH2C;6BAQpC;8BAKA;sCACuB;4BACI;iCACN;mFAIE;iFACF;;;;;;;;;;AAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,gBAAU;AAE5B,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;IACxE,MAAM,EAAE,YAAY,EAAE,GAAG,IAAA,aAAQ,EAAC;IAElC,IAAA,gBAAS,EAAC;QACR,4BAA4B;QAC5B,IAAI,yBAAA,mCAAA,aAAc,WAAW,EAAE;YAC7B,aAAa;YACb;QACF,OAAO;YACL,aAAa;YACb,WAAW;QACb;IACF,GAAG;QAAC,yBAAA,mCAAA,aAAc,WAAW;KAAC;IAE9B,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,qBAAW,CAAC,oBAAoB;YACrD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,YAAO,CAAC,IAAI,CAAC;IACf;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,IAAI,QAAQ,YAAY,CAAC,eAAc,yBAAA,mCAAA,aAAc,WAAW,GAC9D;IAEJ;IAEA,MAAM,WAAW;QACf;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,mBAAY;;;;;oBAAG;;;;;;;YAIpB,wBACE,2BAAC,0BAAiB;gBAChB,YAAY;gBACZ,SAAS;gBACT,WAAW;;;;;;YAGf,UAAU,EAAC,yBAAA,mCAAA,aAAc,WAAW;QACtC;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,4BAAqB;;;;;oBAAG;;;;;;;YAI7B,wBAAU,2BAAC,wBAAe;;;;;QAC5B;KACD;IAED,qBACE,2BAAC,4BAAa;QACZ,OAAM;QACN,qBACE,2BAAC,YAAM;YACL,MAAK;YACL,oBAAM,2BAAC,mBAAY;;;;;YACnB,SAAS;sBACV;;;;;;kBAKH,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,MAAK;;;;;;;;;;;;;;;;AAKf;GA9FM;;QAIqB,aAAQ;;;KAJ7B;IAgGN,WAAe"}