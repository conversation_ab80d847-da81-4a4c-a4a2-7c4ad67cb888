((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__user__index'],
{ "src/pages/user/index.tsx": function (module, exports, __mako_require__){
/**
 * 用户管理页面 - 合并个人资料和账户设置
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _UserProfileContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/user/components/UserProfileContent.tsx"));
var _UserSettingsContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/user/components/UserSettingsContent.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title } = _antd.Typography;
const UserManagePage = ()=>{
    _s();
    const [activeTab, setActiveTab] = (0, _react.useState)('profile');
    const tabItems = [
        {
            key: 'profile',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/user/index.tsx",
                        lineNumber: 32,
                        columnNumber: 11
                    }, this),
                    "个人资料"
                ]
            }, void 0, true, {
                fileName: "src/pages/user/index.tsx",
                lineNumber: 31,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserProfileContent.default, {}, void 0, false, {
                fileName: "src/pages/user/index.tsx",
                lineNumber: 36,
                columnNumber: 17
            }, this)
        },
        {
            key: 'settings',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                        fileName: "src/pages/user/index.tsx",
                        lineNumber: 42,
                        columnNumber: 11
                    }, this),
                    "账户设置"
                ]
            }, void 0, true, {
                fileName: "src/pages/user/index.tsx",
                lineNumber: 41,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserSettingsContent.default, {}, void 0, false, {
                fileName: "src/pages/user/index.tsx",
                lineNumber: 46,
                columnNumber: 17
            }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "用户管理",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: setActiveTab,
                items: tabItems,
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/user/index.tsx",
                lineNumber: 53,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "src/pages/user/index.tsx",
            lineNumber: 52,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/user/index.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
};
_s(UserManagePage, "+fBYb7hZxpZ2jsZbIvUzQFz29ZI=");
_c = UserManagePage;
var _default = UserManagePage;
var _c;
$RefreshReg$(_c, "UserManagePage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__user__index-async.js.map