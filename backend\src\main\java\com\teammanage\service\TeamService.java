package com.teammanage.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.teammanage.context.TeamContextHolder;
import com.teammanage.dto.request.CreateTeamRequest;
import com.teammanage.dto.request.InviteMembersRequest;
import com.teammanage.dto.request.UpdateTeamRequest;
import com.teammanage.dto.response.TeamDetailResponse;
import com.teammanage.dto.response.TeamMemberResponse;
import com.teammanage.entity.Account;
import com.teammanage.entity.Team;
import com.teammanage.entity.TeamMember;
import com.teammanage.exception.BusinessException;
import com.teammanage.exception.ResourceNotFoundException;
import com.teammanage.mapper.AccountMapper;
import com.teammanage.mapper.TeamMapper;
import com.teammanage.mapper.TeamMemberMapper;
import com.teammanage.util.TeamPermissionChecker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队管理服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class TeamService {

    private static final Logger log = LoggerFactory.getLogger(TeamService.class);

    @Autowired
    private TeamMapper teamMapper;

    @Autowired
    private TeamMemberMapper teamMemberMapper;

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private TeamPermissionChecker permissionChecker;

    @Value("${app.team.max-members:100}")
    private int maxMembers;

    /**
     * 创建团队
     * 
     * @param request 创建团队请求
     * @param creatorId 创建者ID
     * @return 团队详情
     */
    @Transactional
    public TeamDetailResponse createTeam(CreateTeamRequest request, Long creatorId) {
        // 检查团队名称是否已存在
        if (teamMapper.existsByName(request.getName())) {
            throw new BusinessException("团队名称已存在");
        }

        // 创建团队
        Team team = new Team();
        team.setName(request.getName());
        team.setDescription(request.getDescription());
        team.setCreatedBy(creatorId);
        team.setIsDeleted(false);

        teamMapper.insert(team);

        // 添加创建者为团队成员
        TeamMember creatorMember = new TeamMember();
        creatorMember.setTeamId(team.getId());
        creatorMember.setAccountId(creatorId);
        creatorMember.setIsCreator(true);
        creatorMember.setAssignedAt(LocalDateTime.now());
        creatorMember.setLastAccessTime(LocalDateTime.now());
        creatorMember.setIsActive(true);
        creatorMember.setIsDeleted(false);

        teamMemberMapper.insert(creatorMember);

        log.info("团队创建成功: teamId={}, name={}, creatorId={}", 
                team.getId(), team.getName(), creatorId);

        return getTeamDetail(team.getId());
    }

    /**
     * 获取团队详情
     * 
     * @param teamId 团队ID
     * @return 团队详情
     */
    public TeamDetailResponse getTeamDetail(Long teamId) {
        Team team = teamMapper.selectById(teamId);
        if (team == null || team.getIsDeleted()) {
            throw new ResourceNotFoundException("团队不存在");
        }

        // 统计成员数量
        int memberCount = teamMemberMapper.countByTeamId(teamId);

        TeamDetailResponse response = new TeamDetailResponse();
        response.setId(team.getId());
        response.setName(team.getName());
        response.setDescription(team.getDescription());
        response.setCreatedBy(team.getCreatedBy());
        response.setMemberCount(memberCount);
        response.setCreatedAt(team.getCreatedAt());
        response.setUpdatedAt(team.getUpdatedAt());

        return response;
    }

    /**
     * 获取当前团队详情
     * 
     * @return 团队详情
     */
    public TeamDetailResponse getCurrentTeamDetail() {
        Long teamId = TeamContextHolder.getCurrentTeamId();
        return getTeamDetail(teamId);
    }

    /**
     * 更新团队信息
     * 
     * @param request 更新请求
     * @return 更新后的团队详情
     */
    @Transactional
    public TeamDetailResponse updateTeam(UpdateTeamRequest request) {
        // 检查权限
        permissionChecker.checkTeamManagePermission();

        Long teamId = TeamContextHolder.getCurrentTeamId();
        Team team = teamMapper.selectById(teamId);
        if (team == null || team.getIsDeleted()) {
            throw new ResourceNotFoundException("团队不存在");
        }

        // 检查团队名称是否已被其他团队使用
        if (!team.getName().equals(request.getName()) && teamMapper.existsByName(request.getName())) {
            throw new BusinessException("团队名称已存在");
        }

        // 更新团队信息
        team.setName(request.getName());
        team.setDescription(request.getDescription());
        teamMapper.updateById(team);

        log.info("团队信息更新成功: teamId={}, name={}", teamId, request.getName());

        return getTeamDetail(teamId);
    }

    /**
     * 获取团队成员列表
     * 
     * @param page 页码
     * @param size 页大小
     * @return 成员列表
     */
    public Page<TeamMemberResponse> getTeamMembers(int page, int size) {
        Long teamId = TeamContextHolder.getCurrentTeamId();
        
        QueryWrapper<TeamMember> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("team_id", teamId)
                   .eq("is_active", true)
                   .eq("is_deleted", false)
                   .orderByDesc("is_creator")
                   .orderByDesc("assigned_at");

        Page<TeamMember> memberPage = teamMemberMapper.selectPage(new Page<>(page, size), queryWrapper);
        
        // 转换为响应DTO
        List<TeamMemberResponse> memberResponses = memberPage.getRecords().stream()
                .map(this::convertToMemberResponse)
                .collect(Collectors.toList());

        Page<TeamMemberResponse> responsePage = new Page<>(page, size);
        responsePage.setRecords(memberResponses);
        responsePage.setTotal(memberPage.getTotal());
        responsePage.setPages(memberPage.getPages());

        return responsePage;
    }

    /**
     * 邀请成员
     * 
     * @param request 邀请请求
     */
    @Transactional
    public void inviteMembers(InviteMembersRequest request) {
        // 检查权限
        permissionChecker.checkMemberManagePermission();

        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long inviterId = TeamContextHolder.getCurrentUserId();

        // 检查团队成员数量限制
        int currentMemberCount = teamMemberMapper.countByTeamId(teamId);
        if (currentMemberCount + request.getEmails().size() > maxMembers) {
            throw new BusinessException("团队成员数量超出限制，最多允许" + maxMembers + "名成员");
        }

        for (String email : request.getEmails()) {
            try {
                inviteMember(teamId, email, inviterId);
            } catch (Exception e) {
                log.warn("邀请成员失败: email={}, error={}", email, e.getMessage());
                // 继续处理其他邮箱，不中断整个流程
            }
        }

        log.info("批量邀请成员完成: teamId={}, inviterCount={}", teamId, request.getEmails().size());
    }

    /**
     * 邀请单个成员
     */
    private void inviteMember(Long teamId, String email, Long inviterId) {
        // 查找用户
        Account account = accountMapper.findByEmail(email);
        if (account == null) {
            throw new BusinessException("用户不存在: " + email);
        }

        // 检查是否已是团队成员
        TeamMember existingMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, account.getId());
        if (existingMember != null && !existingMember.getIsDeleted()) {
            throw new BusinessException("用户已是团队成员: " + email);
        }

        // 添加团队成员
        TeamMember member = new TeamMember();
        member.setTeamId(teamId);
        member.setAccountId(account.getId());
        member.setIsCreator(false);
        member.setAssignedAt(LocalDateTime.now());
        member.setIsActive(true);
        member.setIsDeleted(false);

        teamMemberMapper.insert(member);

        log.info("成员邀请成功: teamId={}, email={}, memberId={}", teamId, email, account.getId());
    }

    /**
     * 移除团队成员
     * 
     * @param memberId 成员ID
     */
    @Transactional
    public void removeMember(Long memberId) {
        // 检查权限
        permissionChecker.checkMemberManagePermission();

        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long currentUserId = TeamContextHolder.getCurrentUserId();

        TeamMember member = teamMemberMapper.selectById(memberId);
        if (member == null || member.getIsDeleted() || !member.getTeamId().equals(teamId)) {
            throw new ResourceNotFoundException("团队成员不存在");
        }

        // 不能移除自己
        if (member.getAccountId().equals(currentUserId)) {
            throw new BusinessException("不能移除自己");
        }

        // 不能移除其他创建者
        if (member.getIsCreator()) {
            throw new BusinessException("不能移除团队创建者");
        }

        // 软删除成员
        member.setIsDeleted(true);
        member.setIsActive(false);
        teamMemberMapper.updateById(member);

        log.info("团队成员移除成功: teamId={}, memberId={}, accountId={}", 
                teamId, memberId, member.getAccountId());
    }

    /**
     * 获取用户参与的团队列表
     * 
     * @param userId 用户ID
     * @return 团队列表
     */
    public List<TeamDetailResponse> getUserTeams(Long userId) {
        List<TeamMember> teamMembers = teamMemberMapper.findByAccountId(userId);
        
        return teamMembers.stream()
                .map(member -> {
                    TeamDetailResponse response = getTeamDetail(member.getTeamId());
                    response.setIsCreator(member.getIsCreator());
                    response.setLastAccessTime(member.getLastAccessTime());
                    return response;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换团队成员为响应DTO
     */
    private TeamMemberResponse convertToMemberResponse(TeamMember member) {
        Account account = accountMapper.selectById(member.getAccountId());
        
        TeamMemberResponse response = new TeamMemberResponse();
        response.setId(member.getId());
        response.setAccountId(member.getAccountId());
        response.setEmail(account != null ? account.getEmail() : "");
        response.setName(account != null ? account.getName() : "");
        response.setIsCreator(member.getIsCreator());
        response.setAssignedAt(member.getAssignedAt());
        response.setLastAccessTime(member.getLastAccessTime());
        response.setIsActive(member.getIsActive());
        
        return response;
    }

}
