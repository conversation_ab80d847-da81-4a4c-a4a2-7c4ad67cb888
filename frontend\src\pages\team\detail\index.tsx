/**
 * 团队详情页面
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Descriptions, 
  Button, 
  Space, 
  Typography, 
  message, 
  Modal, 
  Form, 
  Input,
  Spin,
  Divider
} from 'antd';
import {
  TeamOutlined,
  EditOutlined,
  UserAddOutlined,
  SettingOutlined,
  SwapOutlined
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { TeamService } from '@/services';
import type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';
import TeamMemberList from './components/TeamMemberList';
import InviteMemberModal from './components/InviteMemberModal';
import MemberAssignModal from './components/MemberAssignModal';

const { Title, Text } = Typography;
const { TextArea } = Input;

const TeamDetailPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [inviteModalVisible, setInviteModalVisible] = useState(false);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchTeamDetail();
  }, []);

  const fetchTeamDetail = async () => {
    try {
      setLoading(true);
      const detail = await TeamService.getCurrentTeamDetail();
      setTeamDetail(detail);
    } catch (error) {
      console.error('获取团队详情失败:', error);
      message.error('获取团队详情失败');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    if (teamDetail) {
      form.setFieldsValue({
        name: teamDetail.name,
        description: teamDetail.description,
      });
      setEditModalVisible(true);
    }
  };

  const handleUpdate = async (values: UpdateTeamRequest) => {
    try {
      setUpdating(true);
      const updatedTeam = await TeamService.updateCurrentTeam(values);
      setTeamDetail(updatedTeam);
      setEditModalVisible(false);
      message.success('团队信息更新成功');
    } catch (error) {
      console.error('更新团队信息失败:', error);
    } finally {
      setUpdating(false);
    }
  };

  const handleInviteSuccess = () => {
    setInviteModalVisible(false);
    fetchTeamDetail(); // 刷新团队详情
    message.success('邀请发送成功');
  };

  const handleAssignSuccess = () => {
    setAssignModalVisible(false);
    fetchTeamDetail(); // 刷新团队详情
    message.success('成员分配成功');
  };

  if (loading) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
        </div>
      </PageContainer>
    );
  }

  if (!teamDetail) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Text type="secondary">团队信息加载失败</Text>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title={
        <Space>
          <TeamOutlined />
          {teamDetail.name}
          {teamDetail.isCreator && (
            <Text type="secondary" style={{ fontSize: 14 }}>
              (管理员)
            </Text>
          )}
        </Space>
      }
      extra={
        teamDetail.isCreator ? (
          <Space>
            <Button
              type="primary"
              icon={<UserAddOutlined />}
              onClick={() => setInviteModalVisible(true)}
            >
              邀请成员
            </Button>
            <Button
              icon={<SwapOutlined />}
              onClick={() => setAssignModalVisible(true)}
            >
              分配成员
            </Button>
            <Button
              icon={<EditOutlined />}
              onClick={handleEdit}
            >
              编辑团队
            </Button>
          </Space>
        ) : null
      }
    >
      <Card title="团队信息" style={{ marginBottom: 24 }}>
        <Descriptions column={2}>
          <Descriptions.Item label="团队名称">
            {teamDetail.name}
          </Descriptions.Item>
          <Descriptions.Item label="成员数量">
            {teamDetail.memberCount} 人
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {new Date(teamDetail.createdAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="最后更新">
            {new Date(teamDetail.updatedAt).toLocaleString()}
          </Descriptions.Item>
          {teamDetail.description && (
            <Descriptions.Item label="团队描述" span={2}>
              {teamDetail.description}
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>

      <TeamMemberList 
        teamId={teamDetail.id}
        isCreator={teamDetail.isCreator}
        onMemberChange={fetchTeamDetail}
      />

      {/* 编辑团队模态框 */}
      <Modal
        title="编辑团队信息"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdate}
        >
          <Form.Item
            label="团队名称"
            name="name"
            rules={[
              { required: true, message: '请输入团队名称！' },
              { max: 100, message: '团队名称长度不能超过100字符！' },
            ]}
          >
            <Input placeholder="请输入团队名称" />
          </Form.Item>

          <Form.Item
            label="团队描述"
            name="description"
            rules={[
              { max: 500, message: '团队描述长度不能超过500字符！' },
            ]}
          >
            <TextArea
              placeholder="请输入团队描述（可选）"
              rows={4}
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={updating}
              >
                保存
              </Button>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 邀请成员模态框 */}
      <InviteMemberModal
        visible={inviteModalVisible}
        onCancel={() => setInviteModalVisible(false)}
        onSuccess={handleInviteSuccess}
      />

      {/* 成员分配模态框 */}
      <MemberAssignModal
        visible={assignModalVisible}
        onCancel={() => setAssignModalVisible(false)}
        onSuccess={handleAssignSuccess}
        currentTeamId={teamDetail.id}
      />
    </PageContainer>
  );
};

export default TeamDetailPage;
