/**
 * 用户管理页面 - 合并个人资料和账户设置
 */

import React, { useState } from 'react';
import { 
  Card, 
  Tabs, 
  Typography, 
  Space
} from 'antd';
import {
  UserOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';

// 导入原有的组件内容
import UserProfileContent from './components/UserProfileContent';
import UserSettingsContent from './components/UserSettingsContent';

const { Title } = Typography;

const UserManagePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile');

  const tabItems = [
    {
      key: 'profile',
      label: (
        <Space>
          <UserOutlined />
          个人资料
        </Space>
      ),
      children: <UserProfileContent />
    },
    {
      key: 'settings',
      label: (
        <Space>
          <SettingOutlined />
          账户设置
        </Space>
      ),
      children: <UserSettingsContent />
    }
  ];

  return (
    <PageContainer title="用户管理">
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </Card>
    </PageContainer>
  );
};

export default UserManagePage;
