/**
 * 团队管理页面 - 合并团队详情和团队列表
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Typography, 
  message,
  Button,
  Space
} from 'antd';
import {
  TeamOutlined,
  UnorderedListOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { history, useModel } from '@umijs/max';
import { TeamService } from '@/services';
import type { TeamDetailResponse } from '@/types/api';

// 导入原有的组件
import TeamDetailContent from './detail/components/TeamDetailContent';
import TeamListContent from './components/TeamListContent';

const { Title } = Typography;

const TeamManagePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('detail');
  const [loading, setLoading] = useState(true);
  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);
  const { initialState } = useModel('@@initialState');

  useEffect(() => {
    // 如果有当前团队，默认显示团队详情；否则显示团队列表
    if (initialState?.currentTeam) {
      setActiveTab('detail');
      fetchTeamDetail();
    } else {
      setActiveTab('list');
      setLoading(false);
    }
  }, [initialState?.currentTeam]);

  const fetchTeamDetail = async () => {
    try {
      setLoading(true);
      const detail = await TeamService.getCurrentTeamDetail();
      setTeamDetail(detail);
    } catch (error) {
      console.error('获取团队详情失败:', error);
      message.error('获取团队详情失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTeam = () => {
    history.push('/team/create');
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    if (key === 'detail' && !teamDetail && initialState?.currentTeam) {
      fetchTeamDetail();
    }
  };

  const tabItems = [
    {
      key: 'detail',
      label: (
        <Space>
          <TeamOutlined />
          当前团队
        </Space>
      ),
      children: (
        <TeamDetailContent 
          teamDetail={teamDetail}
          loading={loading}
          onRefresh={fetchTeamDetail}
        />
      ),
      disabled: !initialState?.currentTeam
    },
    {
      key: 'list',
      label: (
        <Space>
          <UnorderedListOutlined />
          我的团队
        </Space>
      ),
      children: <TeamListContent />
    }
  ];

  return (
    <PageContainer
      title="团队管理"
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreateTeam}
        >
          创建团队
        </Button>
      }
    >
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          items={tabItems}
          size="large"
        />
      </Card>
    </PageContainer>
  );
};

export default TeamManagePage;
