package com.teammanage.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.teammanage.common.ApiResponse;
import com.teammanage.dto.request.CreateTeamRequest;
import com.teammanage.dto.request.InviteMembersRequest;
import com.teammanage.dto.request.UpdateTeamRequest;
import com.teammanage.dto.response.TeamDetailResponse;
import com.teammanage.dto.response.TeamMemberResponse;
import com.teammanage.service.TeamService;
import com.teammanage.util.SecurityUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 团队控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/teams")
@Tag(name = "团队管理", description = "团队管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class TeamController {

    private static final Logger log = LoggerFactory.getLogger(TeamController.class);

    @Autowired
    private TeamService teamService;

    /**
     * 创建团队（需要Account Token）
     */
    @PostMapping
    @Operation(summary = "创建团队", description = "创建新团队，需要Account Token")
    public ApiResponse<TeamDetailResponse> createTeam(@Valid @RequestBody CreateTeamRequest request) {
        Long creatorId = SecurityUtil.getCurrentUserId();
        TeamDetailResponse response = teamService.createTeam(request, creatorId);
        return ApiResponse.success("团队创建成功", response);
    }

    /**
     * 获取用户的团队列表（需要Account Token）
     */
    @GetMapping
    @Operation(summary = "获取团队列表", description = "获取当前用户参与的所有团队，需要Account Token")
    public ApiResponse<List<TeamDetailResponse>> getUserTeams() {
        Long userId = SecurityUtil.getCurrentUserId();
        List<TeamDetailResponse> response = teamService.getUserTeams(userId);
        return ApiResponse.success(response);
    }

    /**
     * 获取当前团队详情（需要Team Token）
     */
    @GetMapping("/current")
    @Operation(summary = "获取当前团队详情", description = "获取当前团队的详细信息，需要Team Token")
    public ApiResponse<TeamDetailResponse> getCurrentTeamDetail() {
        TeamDetailResponse response = teamService.getCurrentTeamDetail();
        return ApiResponse.success(response);
    }

    /**
     * 更新当前团队信息（需要Team Token，仅创建者）
     */
    @PostMapping("/current/update")
    @Operation(summary = "更新团队信息", description = "更新当前团队信息，需要Team Token且仅创建者可操作")
    public ApiResponse<TeamDetailResponse> updateCurrentTeam(@Valid @RequestBody UpdateTeamRequest request) {
        TeamDetailResponse response = teamService.updateTeam(request);
        return ApiResponse.success("团队信息更新成功", response);
    }

    /**
     * 获取当前团队成员列表（需要Team Token）
     */
    @GetMapping("/current/members")
    @Operation(summary = "获取团队成员", description = "获取当前团队的成员列表，需要Team Token")
    public ApiResponse<Page<TeamMemberResponse>> getTeamMembers(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size) {
        Page<TeamMemberResponse> response = teamService.getTeamMembers(page, size);
        return ApiResponse.success(response);
    }

    /**
     * 邀请团队成员（需要Team Token，仅创建者）
     */
    @PostMapping("/current/members/invite")
    @Operation(summary = "邀请团队成员", description = "邀请新成员加入当前团队，需要Team Token且仅创建者可操作")
    public ApiResponse<Void> inviteMembers(@Valid @RequestBody InviteMembersRequest request) {
        teamService.inviteMembers(request);
        return ApiResponse.<Void>success("成员邀请成功", null);
    }

    /**
     * 移除团队成员（需要Team Token，仅创建者）
     */
    @PostMapping("/current/members/remove")
    @Operation(summary = "移除团队成员", description = "从当前团队移除指定成员，需要Team Token且仅创建者可操作")
    public ApiResponse<Void> removeMember(
            @Parameter(description = "成员ID") @RequestParam Long memberId) {
        teamService.removeMember(memberId);
        return ApiResponse.<Void>success("成员移除成功", null);
    }

}
