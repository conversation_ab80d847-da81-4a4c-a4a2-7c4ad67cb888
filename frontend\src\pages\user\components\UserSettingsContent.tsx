/**
 * 用户设置内容组件
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Select, 
  Switch, 
  Button, 
  Space, 
  Typography, 
  message, 
  Divider,
  Radio,
  Alert
} from 'antd';
import { 
  SaveOutlined,
  GlobalOutlined,
  BellOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { UserService } from '@/services';

const { Title, Text } = Typography;
const { Option } = Select;

interface UserPreferences {
  language: string;
  timezone: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    push: boolean;
    teamInvites: boolean;
    systemUpdates: boolean;
  };
}

const UserSettingsContent: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchUserPreferences();
  }, []);

  const fetchUserPreferences = async () => {
    try {
      setLoading(true);
      const prefs = await UserService.getUserPreferences();
      setPreferences(prefs);
      form.setFieldsValue({
        language: prefs.language,
        timezone: prefs.timezone,
        theme: prefs.theme,
        emailNotifications: prefs.notifications.email,
        pushNotifications: prefs.notifications.push,
        teamInviteNotifications: prefs.notifications.teamInvites,
        systemUpdateNotifications: prefs.notifications.systemUpdates,
      });
    } catch (error) {
      console.error('获取用户偏好设置失败:', error);
      message.error('获取用户偏好设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async (values: any) => {
    try {
      setSaving(true);
      const updateData = {
        language: values.language,
        timezone: values.timezone,
        theme: values.theme,
        notifications: {
          email: values.emailNotifications,
          push: values.pushNotifications,
          teamInvites: values.teamInviteNotifications,
          systemUpdates: values.systemUpdateNotifications,
        },
      };
      
      await UserService.updateUserPreferences(updateData);
      setPreferences({ ...preferences!, ...updateData });
      message.success('设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading || !preferences) {
    return <div>加载中...</div>;
  }

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSaveSettings}
      initialValues={{
        language: preferences.language,
        timezone: preferences.timezone,
        theme: preferences.theme,
        emailNotifications: preferences.notifications.email,
        pushNotifications: preferences.notifications.push,
        teamInviteNotifications: preferences.notifications.teamInvites,
        systemUpdateNotifications: preferences.notifications.systemUpdates,
      }}
    >
      {/* 基础设置 */}
      <Card title={<><GlobalOutlined /> 基础设置</>} style={{ marginBottom: 24 }}>
        <Form.Item
          label="界面语言"
          name="language"
          tooltip="选择您偏好的界面显示语言"
        >
          <Select placeholder="请选择语言">
            <Option value="zh-CN">简体中文</Option>
            <Option value="en-US">English</Option>
            <Option value="ja-JP">日本語</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="时区设置"
          name="timezone"
          tooltip="选择您所在的时区，影响时间显示"
        >
          <Select placeholder="请选择时区">
            <Option value="Asia/Shanghai">中国标准时间 (UTC+8)</Option>
            <Option value="America/New_York">美国东部时间 (UTC-5)</Option>
            <Option value="Europe/London">英国时间 (UTC+0)</Option>
            <Option value="Asia/Tokyo">日本标准时间 (UTC+9)</Option>
          </Select>
        </Form.Item>
      </Card>

      {/* 外观设置 */}
      <Card title={<><EyeOutlined /> 外观设置</>} style={{ marginBottom: 24 }}>
        <Form.Item
          label="主题模式"
          name="theme"
          tooltip="选择您偏好的界面主题"
        >
          <Radio.Group>
            <Radio value="light">浅色模式</Radio>
            <Radio value="dark">深色模式</Radio>
            <Radio value="auto">跟随系统</Radio>
          </Radio.Group>
        </Form.Item>

        <Alert
          message="主题设置"
          description="深色模式可以减少眼部疲劳，特别适合在光线较暗的环境中使用。"
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      </Card>

      {/* 通知设置 */}
      <Card title={<><BellOutlined /> 通知设置</>} style={{ marginBottom: 24 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text strong>邮件通知</Text>
              <br />
              <Text type="secondary">接收重要事件的邮件通知</Text>
            </div>
            <Form.Item name="emailNotifications" valuePropName="checked" style={{ margin: 0 }}>
              <Switch />
            </Form.Item>
          </div>

          <Divider style={{ margin: '12px 0' }} />

          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text strong>浏览器推送通知</Text>
              <br />
              <Text type="secondary">在浏览器中接收实时通知</Text>
            </div>
            <Form.Item name="pushNotifications" valuePropName="checked" style={{ margin: 0 }}>
              <Switch />
            </Form.Item>
          </div>

          <Divider style={{ margin: '12px 0' }} />

          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text strong>团队邀请通知</Text>
              <br />
              <Text type="secondary">当有人邀请您加入团队时通知</Text>
            </div>
            <Form.Item name="teamInviteNotifications" valuePropName="checked" style={{ margin: 0 }}>
              <Switch />
            </Form.Item>
          </div>

          <Divider style={{ margin: '12px 0' }} />

          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text strong>系统更新通知</Text>
              <br />
              <Text type="secondary">接收系统功能更新和维护通知</Text>
            </div>
            <Form.Item name="systemUpdateNotifications" valuePropName="checked" style={{ margin: 0 }}>
              <Switch />
            </Form.Item>
          </div>
        </Space>
      </Card>

      {/* 保存按钮 */}
      <Card>
        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={saving}
              icon={<SaveOutlined />}
              size="large"
            >
              保存设置
            </Button>
            <Button
              onClick={() => {
                form.resetFields();
                message.info('已重置为上次保存的设置');
              }}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Card>
    </Form>
  );
};

export default UserSettingsContent;
